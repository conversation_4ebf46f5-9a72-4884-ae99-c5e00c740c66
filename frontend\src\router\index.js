import { createRouter, createWebHistory } from 'vue-router';
import Home from '../views/Home.vue';
import User from '../views/User.vue';
import Department from '../views/Department.vue';
import Goal from '../views/Goal.vue';
import Performance from '../views/Performance.vue';
import Project from '../views/Project.vue';
import ProjectDetail from '../views/ProjectDetail.vue';
import TaskBoard from '../views/TaskBoard.vue';
import TaskDetailTest from '../views/TaskDetailTest.vue';

const routes = [
  { path: '/', component: Home },
  { path: '/user', component: User },
  { path: '/department', component: Department },
  { path: '/goal', component: Goal },
  { path: '/performance', component: Performance },
  { path: '/project', component: Project },
  { path: '/project/:id', component: ProjectDetail },
  { path: '/task-board', component: TaskBoard },
  { path: '/task-detail-test', component: TaskDetailTest },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

export default router; 