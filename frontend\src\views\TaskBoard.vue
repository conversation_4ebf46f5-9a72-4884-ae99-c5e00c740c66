<template>
  <div class="task-board-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">
          <el-icon><Grid /></el-icon>
          任务看板
        </h2>
        <div class="project-selector">
          <el-select v-model="selectedProject" placeholder="选择项目">
            <el-option
              v-for="project in projects"
              :key="project.id"
              :label="project.name"
              :value="project.id"
            />
          </el-select>
        </div>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Plus" @click="showCreateTaskDialog = true">新建任务</el-button>
        <el-button :icon="Filter">筛选</el-button>
        <el-button :icon="Setting">设置</el-button>
      </div>
    </div>

    <!-- 看板内容 -->
    <div class="board-content">
      <div class="board-columns">
        <div
          v-for="column in columns"
          :key="column.id"
          class="board-column"
        >
          <!-- 列头 -->
          <div class="column-header">
            <div class="column-title">
              <span class="column-name">{{ column.name }}</span>
              <span class="task-count">{{ getTaskCount(column.id) }}</span>
            </div>
            <div class="column-progress">
              <span class="progress-text">{{ getColumnProgress(column.id) }}%</span>
            </div>
          </div>

          <!-- 任务列表 -->
          <div class="column-content">
            <draggable
              v-model="column.tasks"
              group="tasks"
              @change="handleTaskMove"
              class="task-list"
            >
              <template #item="{ element: task }">
                <div class="task-card" @click="openTaskDetail(task)">
                  <!-- 任务优先级 -->
                  <div class="task-priority" :class="task.priority"></div>

                  <!-- 任务内容 -->
                  <div class="task-content">
                    <div class="task-title">{{ task.title }}</div>
                    <div class="task-description" v-if="task.description">
                      {{ task.description }}
                    </div>

                    <!-- 任务标签 -->
                    <div class="task-tags" v-if="task.tags && task.tags.length">
                      <el-tag
                        v-for="tag in task.tags"
                        :key="tag"
                        size="small"
                        class="task-tag"
                      >
                        {{ tag }}
                      </el-tag>
                    </div>

                    <!-- 任务底部信息 -->
                    <div class="task-footer">
                      <div class="task-meta">
                        <span class="task-id">#{{ task.id }}</span>
                        <span class="task-date">{{ formatDate(task.dueDate) }}</span>
                      </div>
                      <div class="task-assignee" v-if="task.assignee">
                        <el-avatar :size="24" :src="task.assignee.avatar" />
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </draggable>

            <!-- 添加任务按钮 -->
            <div class="add-task-btn" @click="addTaskToColumn(column.id)">
              <el-icon><Plus /></el-icon>
              <span>添加任务</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 新建任务对话框 -->
    <el-dialog v-model="showCreateTaskDialog" title="新建任务" width="600px">
      <el-form :model="newTask" label-width="80px">
        <el-form-item label="任务标题" required>
          <el-input v-model="newTask.title" placeholder="请输入任务标题" />
        </el-form-item>
        <el-form-item label="任务描述">
          <el-input
            v-model="newTask.description"
            type="textarea"
            :rows="3"
            placeholder="请输入任务描述"
          />
        </el-form-item>
        <el-form-item label="优先级">
          <el-select v-model="newTask.priority">
            <el-option label="高" value="high" />
            <el-option label="中" value="medium" />
            <el-option label="低" value="low" />
          </el-select>
        </el-form-item>
        <el-form-item label="负责人">
          <el-select v-model="newTask.assigneeId" placeholder="选择负责人">
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.name"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="截止日期">
          <el-date-picker
            v-model="newTask.dueDate"
            type="date"
            placeholder="选择截止日期"
          />
        </el-form-item>
        <el-form-item label="标签">
          <el-select
            v-model="newTask.tags"
            multiple
            filterable
            allow-create
            placeholder="选择或创建标签"
          >
            <el-option
              v-for="tag in availableTags"
              :key="tag"
              :label="tag"
              :value="tag"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateTaskDialog = false">取消</el-button>
        <el-button type="primary" @click="createTask">创建</el-button>
      </template>
    </el-dialog>

    <!-- 任务详情对话框 -->
    <TaskDetailDialog
      v-model="showTaskDetail"
      :task="selectedTask"
      @task-updated="handleTaskUpdated"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { Grid, Plus, Filter, Setting } from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import TaskDetailDialog from '../components/TaskDetailDialog.vue'

// 响应式数据
const selectedProject = ref(1)
const showCreateTaskDialog = ref(false)

// 新建任务表单
const newTask = ref({
  title: '',
  description: '',
  priority: 'medium',
  assigneeId: null,
  dueDate: null,
  tags: []
})

// 模拟数据
const projects = ref([
  { id: 1, name: '企业管理系统' },
  { id: 2, name: 'ORK系统' },
  { id: 3, name: '移动应用开发' }
])

const users = ref([
  { id: 1, name: '张三', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
  { id: 2, name: '李四', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
  { id: 3, name: '王五', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' }
])

const availableTags = ref(['前端', '后端', '设计', '测试', '紧急', '优化'])

// 看板列配置
const columns = ref([
  {
    id: 'backlog',
    name: '待开始',
    tasks: [
      {
        id: 1,
        title: '用户界面设计',
        description: '设计用户登录和注册界面',
        priority: 'high',
        tags: ['设计', '前端'],
        assignee: { id: 1, name: '张三', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
        dueDate: '2024-06-15'
      },
      {
        id: 2,
        title: '数据库设计',
        description: '设计用户表和权限表结构',
        priority: 'medium',
        tags: ['后端'],
        assignee: { id: 2, name: '李四', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
        dueDate: '2024-06-20'
      }
    ]
  },
  {
    id: 'inprogress',
    name: '进行中',
    tasks: [
      {
        id: 3,
        title: 'API接口开发',
        description: '开发用户认证相关API',
        priority: 'high',
        tags: ['后端', '紧急'],
        assignee: { id: 2, name: '李四', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
        dueDate: '2024-06-18'
      }
    ]
  },
  {
    id: 'testing',
    name: '测试中',
    tasks: [
      {
        id: 4,
        title: '单元测试编写',
        description: '为核心功能编写单元测试',
        priority: 'medium',
        tags: ['测试'],
        assignee: { id: 3, name: '王五', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
        dueDate: '2024-06-22'
      }
    ]
  },
  {
    id: 'done',
    name: '已完成',
    tasks: [
      {
        id: 5,
        title: '项目初始化',
        description: '创建项目结构和基础配置',
        priority: 'low',
        tags: ['后端'],
        assignee: { id: 1, name: '张三', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
        dueDate: '2024-06-10'
      }
    ]
  }
])

// 计算属性和方法
const getTaskCount = (columnId) => {
  const column = columns.value.find(col => col.id === columnId)
  return column ? column.tasks.length : 0
}

const getColumnProgress = (columnId) => {
  // 简单的进度计算逻辑
  const column = columns.value.find(col => col.id === columnId)
  if (!column || column.tasks.length === 0) return 0

  if (columnId === 'done') return 100
  if (columnId === 'testing') return 80
  if (columnId === 'inprogress') return 50
  return 0
}

const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' })
}

const handleTaskMove = (event) => {
  // 处理任务拖拽移动
  console.log('任务移动:', event)
}

const showTaskDetail = ref(false)
const selectedTask = ref({})

const openTaskDetail = (task) => {
  selectedTask.value = task
  showTaskDetail.value = true
}

const handleTaskUpdated = (updatedTask) => {
  // 在所有列中查找并更新任务
  columns.value.forEach(column => {
    const index = column.tasks.findIndex(t => t.id === updatedTask.id)
    if (index !== -1) {
      column.tasks[index] = { ...column.tasks[index], ...updatedTask }
    }
  })
}

const addTaskToColumn = (columnId) => {
  // 快速添加任务到指定列
  newTask.value.columnId = columnId
  showCreateTaskDialog.value = true
}

const createTask = () => {
  // 创建新任务
  const task = {
    id: Date.now(),
    title: newTask.value.title,
    description: newTask.value.description,
    priority: newTask.value.priority,
    tags: newTask.value.tags,
    assignee: users.value.find(u => u.id === newTask.value.assigneeId),
    dueDate: newTask.value.dueDate
  }

  // 添加到指定列或默认添加到待开始列
  const targetColumnId = newTask.value.columnId || 'backlog'
  const targetColumn = columns.value.find(col => col.id === targetColumnId)
  if (targetColumn) {
    targetColumn.tasks.push(task)
  }

  showCreateTaskDialog.value = false

  // 重置表单
  newTask.value = {
    title: '',
    description: '',
    priority: 'medium',
    assigneeId: null,
    dueDate: null,
    tags: []
  }
}

onMounted(() => {
  // 组件挂载后的初始化逻辑
})
</script>

<style scoped>
.task-board-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f6fa;
}

/* 页面头部 */
.page-header {
  background: #fff;
  padding: 20px 24px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.page-title {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.project-selector {
  min-width: 200px;
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 看板内容 */
.board-content {
  flex: 1;
  padding: 24px;
  overflow: hidden;
}

.board-columns {
  display: flex;
  gap: 20px;
  height: 100%;
  overflow-x: auto;
  padding-bottom: 20px;
}

/* 看板列 */
.board-column {
  min-width: 280px;
  width: 280px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.column-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.column-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.column-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.task-count {
  background: #e4e7ed;
  color: #606266;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.column-progress {
  text-align: right;
}

.progress-text {
  font-size: 12px;
  color: #909399;
}

/* 列内容 */
.column-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.task-list {
  min-height: 100px;
}

/* 任务卡片 */
.task-card {
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.task-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.task-priority {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  border-radius: 6px 0 0 6px;
}

.task-priority.high {
  background: #f56c6c;
}

.task-priority.medium {
  background: #e6a23c;
}

.task-priority.low {
  background: #67c23a;
}

.task-content {
  margin-left: 8px;
}

.task-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 6px;
  line-height: 1.4;
}

.task-description {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
  line-height: 1.4;
}

.task-tags {
  margin-bottom: 8px;
}

.task-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}

.task-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-meta {
  display: flex;
  gap: 8px;
  font-size: 12px;
  color: #c0c4cc;
}

.task-id {
  color: #909399;
}

.task-date {
  color: #e6a23c;
}

/* 添加任务按钮 */
.add-task-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px;
  border: 2px dashed #e4e7ed;
  border-radius: 6px;
  color: #909399;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 8px;
}

.add-task-btn:hover {
  border-color: #409eff;
  color: #409eff;
  background: #f0f9ff;
}

/* 拖拽样式 */
.sortable-ghost {
  opacity: 0.5;
}

.sortable-chosen {
  transform: rotate(5deg);
}

/* 滚动条样式 */
.board-columns::-webkit-scrollbar {
  height: 8px;
}

.board-columns::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.board-columns::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.board-columns::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.column-content::-webkit-scrollbar {
  width: 6px;
}

.column-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.column-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}
</style>
