## 企业ORK系统

> 企业级目标管理与绩效考核平台，采用类似Worktile的现代化设计风格，支持目标管理、绩效考核、员工管理、项目管理、任务看板、甘特图等核心功能。

> 帮助企业实现目标分解、绩效考核、员工与项目管理的信息化，提供完整的项目协作和团队管理解决方案，提高管理效率。

> 当前状态：基础功能开发完成，支持前后端分离、数据库初始化、核心模块的增删改查。UI界面已重构为类Worktile风格的现代化工作台，新增项目详情页面系统，包含任务列表、甘特图、成员管理、文档管理、项目设置等五大模块。

> 项目团队：个人/小型团队（可根据实际情况补充）

> 技术栈：前端Vue3+Element Plus，后端Node.js+Express，数据库MySQL，接口RESTful风格。

## Dependencies (init from programming language specification like package.json, requirements.txt, etc.)

* express (^4.18.2): Node.js Web框架
* mysql2 (^3.6.0): Node.js MySQL数据库驱动
* jsonwebtoken (^9.0.0): JWT鉴权
* cors (^2.8.5): 跨域支持
* body-parser (^1.20.2): 请求体解析
* vue (^3.4.0): 前端框架
* element-plus (^2.5.0): UI组件库
* axios (^1.6.0): HTTP请求库
* vue-router (^4.2.0): 路由管理
* pinia (^2.1.0): 状态管理
* vuedraggable (^4.1.0): 拖拽组件库，用于任务看板

## Development Environment

- Node.js 16+
- npm 8+
- MySQL 5.7/8.0
- 推荐使用VSCode、Navicat等工具
- 启动命令见README.md

## Structrue (init from project tree)

```
root
- backend                  # Node.js后端
    - config               # 配置文件
        - db.js            # 数据库连接配置
    - package-lock.json
    - package.json         # 后端依赖
    - src
        - app.js           # Express应用入口
        - controllers      # 业务控制器
            - department.js
            - goal.js
            - performance.js
            - position.js
            - project.js
            - user.js
        - db.js            # MySQL连接池
        - routes           # 路由定义
            - department.js
            - goal.js
            - performance.js
            - position.js
            - project.js
            - user.js
- docs
    - db
        - ork_init.sql     # 数据库建表脚本
        - ork_init_rollback.sql # 回滚脚本
- frontend                 # Vue前端
    - package-lock.json
    - package.json         # 前端依赖
    - src
        - App.vue         # 主框架
        - main.js         # 入口文件
        - router
            - index.js    # 路由配置
        - components      # 组件库
            - project     # 项目相关组件
                - TaskList.vue        # 任务列表组件
                - GanttChart.vue      # 甘特图组件
                - ProjectMembers.vue  # 项目成员组件
                - ProjectDocuments.vue # 项目文档组件
                - ProjectSettings.vue # 项目设置组件
        - views           # 页面视图
            - Department.vue
            - Goal.vue
            - Home.vue          # 工作台首页
            - Performance.vue
            - Project.vue       # 项目列表页
            - ProjectDetail.vue # 项目详情页
            - TaskBoard.vue     # 任务看板页
            - User.vue
    - vue.config.js        # 前端代理配置
- README.md                # 项目说明
```

// 详细说明见各目录下README或注释。