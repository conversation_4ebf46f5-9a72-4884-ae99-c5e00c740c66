const pool = require('../db');

exports.list = async (req, res) => {
  try {
    const [rows] = await pool.query('SELECT * FROM project_file');
    res.json(rows);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.get = async (req, res) => {
  try {
    const [rows] = await pool.query('SELECT * FROM project_file WHERE id = ?', [req.params.id]);
    if (rows.length === 0) return res.status(404).json({ error: '未找到文件' });
    res.json(rows[0]);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.create = async (req, res) => {
  try {
    const { project_id, task_id, filename, filepath, uploader_id } = req.body;
    const [result] = await pool.query(
      'INSERT INTO project_file (project_id, task_id, filename, filepath, uploader_id) VALUES (?, ?, ?, ?, ?)',
      [project_id, task_id, filename, filepath, uploader_id]
    );
    res.json({ id: result.insertId });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.remove = async (req, res) => {
  try {
    const [result] = await pool.query('DELETE FROM project_file WHERE id = ?', [req.params.id]);
    res.json({ affectedRows: result.affectedRows });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
}; 