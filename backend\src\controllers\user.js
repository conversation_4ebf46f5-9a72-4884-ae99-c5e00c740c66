const pool = require('../db');

exports.list = async (req, res) => {
  try {
    const [rows] = await pool.query('SELECT * FROM user');
    res.json(rows);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.get = async (req, res) => {
  try {
    const [rows] = await pool.query('SELECT * FROM user WHERE id = ?', [req.params.id]);
    if (rows.length === 0) return res.status(404).json({ error: '未找到用户' });
    res.json(rows[0]);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.create = async (req, res) => {
  try {
    const { username, realname, password, email, phone, department_id, position_id, status } = req.body;
    const [result] = await pool.query(
      'INSERT INTO user (username, realname, password, email, phone, department_id, position_id, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
      [username, realname, password, email, phone, department_id, position_id, status || 1]
    );
    res.json({ id: result.insertId });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.update = async (req, res) => {
  try {
    const { username, realname, password, email, phone, department_id, position_id, status } = req.body;
    const [result] = await pool.query(
      'UPDATE user SET username=?, realname=?, password=?, email=?, phone=?, department_id=?, position_id=?, status=? WHERE id=?',
      [username, realname, password, email, phone, department_id, position_id, status, req.params.id]
    );
    res.json({ affectedRows: result.affectedRows });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.remove = async (req, res) => {
  try {
    const [result] = await pool.query('DELETE FROM user WHERE id = ?', [req.params.id]);
    res.json({ affectedRows: result.affectedRows });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
}; 