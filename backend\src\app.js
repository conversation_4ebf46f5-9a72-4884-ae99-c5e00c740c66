const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const userRoutes = require('./routes/user');
const departmentRoutes = require('./routes/department');
const positionRoutes = require('./routes/position');
const goalRoutes = require('./routes/goal');
const performanceRoutes = require('./routes/performance');
const projectRoutes = require('./routes/project');

const app = express();
app.use(cors());
app.use(bodyParser.json());

app.use('/api/user', userRoutes);
app.use('/api/department', departmentRoutes);
app.use('/api/position', positionRoutes);
app.use('/api/goal', goalRoutes);
app.use('/api/performance', performanceRoutes);
app.use('/api/project', projectRoutes);

app.get('/', (req, res) => {
  res.send('ORK系统后端API');
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
}); 