const pool = require('../db');

exports.list = async (req, res) => {
  try {
    const [rows] = await pool.query('SELECT * FROM performance');
    res.json(rows);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.get = async (req, res) => {
  try {
    const [rows] = await pool.query('SELECT * FROM performance WHERE id = ?', [req.params.id]);
    if (rows.length === 0) return res.status(404).json({ error: '未找到绩效考核' });
    res.json(rows[0]);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.create = async (req, res) => {
  try {
    const { user_id, goal_id, score, comment, period } = req.body;
    const [result] = await pool.query(
      'INSERT INTO performance (user_id, goal_id, score, comment, period) VALUES (?, ?, ?, ?, ?)',
      [user_id, goal_id, score, comment, period]
    );
    res.json({ id: result.insertId });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.update = async (req, res) => {
  try {
    const { user_id, goal_id, score, comment, period } = req.body;
    const [result] = await pool.query(
      'UPDATE performance SET user_id=?, goal_id=?, score=?, comment=?, period=? WHERE id=?',
      [user_id, goal_id, score, comment, period, req.params.id]
    );
    res.json({ affectedRows: result.affectedRows });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.remove = async (req, res) => {
  try {
    const [result] = await pool.query('DELETE FROM performance WHERE id = ?', [req.params.id]);
    res.json({ affectedRows: result.affectedRows });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
}; 