-- ORK系统数据库初始化脚本
-- 数据库名：ORK

-- 1. 员工表
CREATE TABLE IF NOT EXISTS `user` (
  `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '员工ID',
  `username` VARCHAR(50) NOT NULL COMMENT '用户名',
  `realname` VARCHAR(50) NOT NULL COMMENT '真实姓名',
  `password` VARCHAR(100) NOT NULL COMMENT '密码',
  `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
  `phone` VARCHAR(20) DEFAULT NULL COMMENT '手机号',
  `department_id` INT DEFAULT NULL COMMENT '部门ID',
  `position_id` INT DEFAULT NULL COMMENT '岗位ID',
  `status` TINYINT DEFAULT 1 COMMENT '状态 1在职 0离职',
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='员工表';

-- 2. 部门表
CREATE TABLE IF NOT EXISTS `department` (
  `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '部门ID',
  `name` VARCHAR(50) NOT NULL COMMENT '部门名称',
  `parent_id` INT DEFAULT NULL COMMENT '上级部门ID',
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部门表';

-- 3. 岗位表
CREATE TABLE IF NOT EXISTS `position` (
  `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '岗位ID',
  `name` VARCHAR(50) NOT NULL COMMENT '岗位名称',
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='岗位表';

-- 4. 目标表
CREATE TABLE IF NOT EXISTS `goal` (
  `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '目标ID',
  `title` VARCHAR(100) NOT NULL COMMENT '目标标题',
  `description` TEXT COMMENT '目标描述',
  `type` ENUM('company','department','personal') NOT NULL COMMENT '目标类型',
  `owner_id` INT NOT NULL COMMENT '目标负责人',
  `department_id` INT DEFAULT NULL COMMENT '所属部门',
  `parent_id` INT DEFAULT NULL COMMENT '上级目标ID',
  `start_date` DATE DEFAULT NULL COMMENT '开始日期',
  `end_date` DATE DEFAULT NULL COMMENT '结束日期',
  `progress` DECIMAL(5,2) DEFAULT 0.00 COMMENT '进度百分比',
  `status` TINYINT DEFAULT 1 COMMENT '状态 1进行中 0已完成',
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='目标表';

-- 5. 绩效考核表
CREATE TABLE IF NOT EXISTS `performance` (
  `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '考核ID',
  `user_id` INT NOT NULL COMMENT '被考核员工ID',
  `goal_id` INT DEFAULT NULL COMMENT '关联目标ID',
  `score` DECIMAL(5,2) DEFAULT NULL COMMENT '考核得分',
  `comment` TEXT COMMENT '考核评语',
  `period` VARCHAR(20) NOT NULL COMMENT '考核周期',
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='绩效考核表';

-- 6. 项目表
CREATE TABLE IF NOT EXISTS `project` (
  `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '项目ID',
  `name` VARCHAR(100) NOT NULL COMMENT '项目名称',
  `description` TEXT COMMENT '项目描述',
  `owner_id` INT NOT NULL COMMENT '项目负责人',
  `start_date` DATE DEFAULT NULL COMMENT '开始日期',
  `end_date` DATE DEFAULT NULL COMMENT '结束日期',
  `status` TINYINT DEFAULT 1 COMMENT '状态 1进行中 0已完成',
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目表';

-- 7. 项目成员表
CREATE TABLE IF NOT EXISTS `project_member` (
  `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
  `project_id` INT NOT NULL COMMENT '项目ID',
  `user_id` INT NOT NULL COMMENT '成员员工ID',
  `role` VARCHAR(50) DEFAULT NULL COMMENT '项目角色',
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目成员表';

-- 8. 任务表
CREATE TABLE IF NOT EXISTS `task` (
  `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '任务ID',
  `project_id` INT NOT NULL COMMENT '所属项目ID',
  `title` VARCHAR(100) NOT NULL COMMENT '任务标题',
  `description` TEXT COMMENT '任务描述',
  `assignee_id` INT DEFAULT NULL COMMENT '负责人ID',
  `priority_id` INT DEFAULT NULL COMMENT '优先级ID',
  `status` VARCHAR(20) DEFAULT 'todo' COMMENT '状态(todo/doing/done)',
  `start_date` DATE DEFAULT NULL COMMENT '开始日期',
  `due_date` DATE DEFAULT NULL COMMENT '截止日期',
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务表';

-- 9. 文件表
CREATE TABLE IF NOT EXISTS `project_file` (
  `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '文件ID',
  `project_id` INT NOT NULL COMMENT '所属项目ID',
  `task_id` INT DEFAULT NULL COMMENT '所属任务ID',
  `filename` VARCHAR(255) NOT NULL COMMENT '文件名',
  `filepath` VARCHAR(255) NOT NULL COMMENT '文件路径',
  `uploader_id` INT NOT NULL COMMENT '上传人ID',
  `uploaded_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目文件表';

-- 10. 标签表
CREATE TABLE IF NOT EXISTS `project_tag` (
  `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '标签ID',
  `name` VARCHAR(50) NOT NULL COMMENT '标签名称',
  `color` VARCHAR(20) DEFAULT NULL COMMENT '标签颜色'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签表';

-- 11. 任务标签关联表
CREATE TABLE IF NOT EXISTS `task_tag` (
  `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
  `task_id` INT NOT NULL COMMENT '任务ID',
  `tag_id` INT NOT NULL COMMENT '标签ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务标签关联表';

-- 12. 优先级表
CREATE TABLE IF NOT EXISTS `project_priority` (
  `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '优先级ID',
  `name` VARCHAR(20) NOT NULL COMMENT '优先级名称',
  `level` INT NOT NULL COMMENT '优先级等级(数字越大优先级越高)'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='优先级表'; 