<template>
  <div class="task-detail-test">
    <div class="test-header">
      <h2>任务详情模板测试</h2>
      <p>点击下面的任务卡片查看统一的任务详情模板</p>
    </div>
    
    <div class="test-content">
      <!-- 模拟任务卡片 -->
      <div class="task-cards">
        <div 
          v-for="task in testTasks" 
          :key="task.id"
          class="task-card"
          @click="openTaskDetail(task)"
        >
          <div class="card-header">
            <el-tag :type="getStatusType(task.status)" size="small">
              {{ getStatusText(task.status) }}
            </el-tag>
            <el-tag :type="getPriorityType(task.priority)" size="small">
              {{ getPriorityText(task.priority) }}
            </el-tag>
          </div>
          <div class="card-content">
            <h3 class="task-title">{{ task.title }}</h3>
            <p class="task-description">{{ task.description }}</p>
            <div class="task-meta">
              <div class="assignee-info">
                <el-avatar :size="24" :src="task.creator?.avatar" />
                <span>{{ task.creator?.name }}</span>
              </div>
              <span class="due-date">{{ task.due_date }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 任务详情对话框 -->
    <TaskDetailDialog 
      v-model="showTaskDetail" 
      :task="selectedTask"
      @task-updated="handleTaskUpdated"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import TaskDetailDialog from '../components/TaskDetailDialog.vue'

const showTaskDetail = ref(false)
const selectedTask = ref({})

// 测试任务数据
const testTasks = ref([
  {
    id: 1,
    title: '【示例】部门例会',
    description: '讨论本月工作进展和下月计划',
    status: 'doing',
    priority: 'medium',
    due_date: '2024-06-07',
    creator: {
      name: '张三',
      department: '技术部',
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
    },
    startDate: '2024-06-07 10:00',
    dueDate: '2024-06-07 11:30'
  },
  {
    id: 2,
    title: '【示例】公司年会策划',
    description: '策划公司年会活动方案',
    status: 'todo',
    priority: 'high',
    due_date: '2024-06-15',
    creator: {
      name: '李四',
      department: '行政部',
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
    },
    startDate: '2024-06-10 09:00',
    dueDate: '2024-06-15 18:00'
  },
  {
    id: 3,
    title: '【示例】系统维护',
    description: '定期系统维护和数据备份',
    status: 'done',
    priority: 'low',
    due_date: '2024-06-05',
    creator: {
      name: '王五',
      department: '技术部',
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
    },
    startDate: '2024-06-05 02:00',
    dueDate: '2024-06-05 06:00'
  }
])

const openTaskDetail = (task) => {
  selectedTask.value = task
  showTaskDetail.value = true
}

const handleTaskUpdated = (updatedTask) => {
  const index = testTasks.value.findIndex(t => t.id === updatedTask.id)
  if (index !== -1) {
    testTasks.value[index] = { ...testTasks.value[index], ...updatedTask }
  }
}

const getStatusType = (status) => {
  const typeMap = {
    'todo': 'info',
    'doing': 'warning',
    'done': 'success'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    'todo': '待处理',
    'doing': '进行中',
    'done': '已完成'
  }
  return textMap[status] || status
}

const getPriorityType = (priority) => {
  const typeMap = {
    'high': 'danger',
    'medium': 'warning',
    'low': 'success'
  }
  return typeMap[priority] || 'info'
}

const getPriorityText = (priority) => {
  const textMap = {
    'high': '高优先级',
    'medium': '中优先级',
    'low': '低优先级'
  }
  return textMap[priority] || priority
}
</script>

<style scoped>
.task-detail-test {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 32px;
  
  h2 {
    color: #303133;
    margin-bottom: 8px;
  }
  
  p {
    color: #606266;
    font-size: 14px;
  }
}

.task-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.task-card {
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.task-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-content {
  .task-title {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 8px 0;
  }
  
  .task-description {
    font-size: 14px;
    color: #606266;
    line-height: 1.5;
    margin: 0 0 16px 0;
  }
  
  .task-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .assignee-info {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      color: #303133;
    }
    
    .due-date {
      font-size: 12px;
      color: #909399;
    }
  }
}
</style>
