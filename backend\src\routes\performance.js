const express = require('express');
const router = express.Router();
const performanceController = require('../controllers/performance');

router.get('/', performanceController.list);
router.get('/:id', performanceController.get);
router.post('/', performanceController.create);
router.put('/:id', performanceController.update);
router.delete('/:id', performanceController.remove);

module.exports = router; 