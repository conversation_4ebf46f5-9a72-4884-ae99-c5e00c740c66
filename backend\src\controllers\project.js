const pool = require('../db');

exports.list = async (req, res) => {
  try {
    const [rows] = await pool.query('SELECT * FROM project');
    res.json(rows);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.get = async (req, res) => {
  try {
    const [rows] = await pool.query('SELECT * FROM project WHERE id = ?', [req.params.id]);
    if (rows.length === 0) return res.status(404).json({ error: '未找到项目' });
    res.json(rows[0]);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.create = async (req, res) => {
  try {
    const { name, description, owner_id, start_date, end_date, status } = req.body;
    const [result] = await pool.query(
      'INSERT INTO project (name, description, owner_id, start_date, end_date, status) VALUES (?, ?, ?, ?, ?, ?)',
      [name, description, owner_id, start_date, end_date, status || 1]
    );
    res.json({ id: result.insertId });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.update = async (req, res) => {
  try {
    const { name, description, owner_id, start_date, end_date, status } = req.body;
    const [result] = await pool.query(
      'UPDATE project SET name=?, description=?, owner_id=?, start_date=?, end_date=?, status=? WHERE id=?',
      [name, description, owner_id, start_date, end_date, status, req.params.id]
    );
    res.json({ affectedRows: result.affectedRows });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.remove = async (req, res) => {
  try {
    const [result] = await pool.query('DELETE FROM project WHERE id = ?', [req.params.id]);
    res.json({ affectedRows: result.affectedRows });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
}; 