<template>
  <el-dialog
    v-model="visible"
    :title="task.title"
    width="80%"
    :before-close="handleClose"
    class="task-detail-dialog"
    destroy-on-close
  >
    <!-- 对话框头部 -->
    <template #header>
      <div class="dialog-header">
        <div class="header-left">
          <div class="task-status">
            <el-icon class="status-icon" :class="getStatusClass(task.status)">
              <component :is="getStatusIcon(task.status)" />
            </el-icon>
            <span class="task-title">{{ task.title }}</span>
          </div>
        </div>
        <div class="header-right">
          <el-button text :icon="Edit" @click="editTask">编辑</el-button>
          <el-button text :icon="Share">分享</el-button>
          <el-button text :icon="MoreFilled">更多</el-button>
        </div>
      </div>
    </template>

    <!-- 任务基本信息 -->
    <div class="task-basic-info">
      <div class="info-row">
        <div class="info-item">
          <el-avatar :size="32" :src="task.creator?.avatar" />
          <div class="creator-info">
            <span class="creator-name">{{ task.creator?.name || '负责人' }}</span>
            <span class="create-time">{{ task.creator?.department || '部门' }}</span>
          </div>
        </div>

        <div class="info-item">
          <el-icon><Calendar /></el-icon>
          <div class="time-info">
            <span class="time-label">{{ formatDate(task.startDate) || '6月7日 10:00' }}</span>
            <span class="time-desc">开始时间</span>
          </div>
        </div>

        <div class="info-item">
          <el-icon><Calendar /></el-icon>
          <div class="time-info">
            <span class="time-label" :class="{ overdue: isOverdue(task.dueDate) }">
              {{ formatDate(task.dueDate) || '6月7日 11:30' }}
            </span>
            <span class="time-desc">截止时间</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 标签页内容 -->
    <div class="task-tabs">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <!-- 详论 -->
        <el-tab-pane label="详论" name="details">
          <div class="tab-content">
            <!-- 任务内容 -->
            <div class="task-content-section">
              <div class="section-header">
                <h4>任务内容</h4>
                <el-button text :icon="Edit" size="small">编辑</el-button>
              </div>
              <div class="task-description">
                <el-input
                  v-model="taskContent"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入任务内容..."
                  readonly
                />
              </div>
            </div>

            <!-- 任务清单 -->
            <div class="task-checklist-section">
              <div class="section-header">
                <h4>任务清单</h4>
                <el-button text :icon="Plus" size="small" @click="addChecklistItem">添加</el-button>
              </div>
              <div class="checklist-table">
                <el-table
                  :data="checklistItems"
                  style="width: 100%"
                  :key="`table-${activeTab}`"
                  size="default"
                  :show-header="true"
                  :border="false"
                  :stripe="false"
                >
                  <el-table-column width="50">
                    <template #default="{ row, $index }">
                      <span class="item-number">{{ $index + 1 }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="内容" min-width="200">
                    <template #default="{ row }">
                      <span>{{ row.content }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="时长" width="100">
                    <template #default="{ row }">
                      <span>{{ row.duration }}</span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>

            <!-- 任务附件 -->
            <div class="task-attachments-section">
              <div class="section-header">
                <h4>任务附件</h4>
                <el-button text :icon="Plus" size="small" @click="uploadAttachment">上传</el-button>
              </div>
              <div class="attachments-list" v-if="attachments.length">
                <div
                  v-for="file in attachments"
                  :key="file.id"
                  class="attachment-item"
                >
                  <el-icon class="file-icon"><Document /></el-icon>
                  <span class="file-name">{{ file.name }}</span>
                  <span class="file-size">{{ formatFileSize(file.size) }}</span>
                  <el-button text :icon="Download" @click="downloadFile(file)">下载</el-button>
                </div>
              </div>
              <div v-else class="empty-attachments">
                <el-icon><Document /></el-icon>
                <span>暂无附件</span>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 活动 -->
        <el-tab-pane label="活动" name="activities">
          <div class="tab-content">
            <!-- 评论输入 -->
            <div class="comment-input-section">
              <div class="comment-input">
                <el-input
                  v-model="newComment"
                  type="textarea"
                  :rows="3"
                  placeholder="按M键输入评论"
                  @keydown.ctrl.enter="addComment"
                />
                <div class="comment-actions">
                  <div class="comment-participants">
                    <span>参与人</span>
                    <el-avatar
                      v-for="participant in participants"
                      :key="participant.id"
                      :size="24"
                      :src="participant.avatar"
                      class="participant-avatar"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- 活动列表 -->
            <div class="activity-list">
              <div
                v-for="activity in activities"
                :key="activity.id"
                class="activity-item"
              >
                <el-avatar :size="32" :src="activity.user.avatar" />
                <div class="activity-content">
                  <div class="activity-header">
                    <span class="user-name">{{ activity.user.name }}</span>
                    <span class="activity-time">{{ formatDateTime(activity.createdAt) }}</span>
                  </div>
                  <div class="activity-body">
                    <div v-if="activity.type === 'comment'" class="comment">
                      {{ activity.content }}
                    </div>
                    <div v-else-if="activity.type === 'status_change'" class="status-change">
                      <span>状态</span>
                      <el-tag :type="getStatusType(activity.oldStatus)" size="small">
                        {{ getStatusText(activity.oldStatus) }}
                      </el-tag>
                      <el-icon><Right /></el-icon>
                      <el-tag :type="getStatusType(activity.newStatus)" size="small">
                        {{ getStatusText(activity.newStatus) }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="!activities.length" class="empty-activities">
              <el-icon><ChatDotRound /></el-icon>
              <span>暂无评论</span>
            </div>
          </div>
        </el-tab-pane>

        <!-- 流转 -->
        <el-tab-pane label="流转" name="workflow">
          <div class="tab-content">
            <!-- 流转历史 -->
            <div class="workflow-list">
              <div
                v-for="workflow in workflowHistory"
                :key="workflow.id"
                class="workflow-item"
              >
                <div class="workflow-status">
                  <el-tag :type="getStatusType(workflow.status)" size="small">
                    {{ getStatusText(workflow.status) }}
                  </el-tag>
                </div>
                <div class="workflow-content">
                  <div class="workflow-header">
                    <el-avatar :size="24" :src="workflow.user.avatar" />
                    <span class="user-name">{{ workflow.user.name }}</span>
                    <span class="workflow-time">{{ formatDateTime(workflow.createdAt) }}</span>
                  </div>
                  <div class="workflow-description" v-if="workflow.description">
                    {{ workflow.description }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="!workflowHistory.length" class="empty-workflow">
              <el-icon><Operation /></el-icon>
              <span>暂无流转</span>
            </div>
          </div>
        </el-tab-pane>

        <!-- 状态审批 -->
        <el-tab-pane label="状态审批" name="approval">
          <div class="tab-content">
            <!-- 审批流程 -->
            <div class="approval-list">
              <div
                v-for="approval in approvalHistory"
                :key="approval.id"
                class="approval-item"
              >
                <div class="approval-status">
                  <el-tag :type="getApprovalType(approval.status)" size="small">
                    {{ getApprovalText(approval.status) }}
                  </el-tag>
                </div>
                <div class="approval-content">
                  <div class="approval-header">
                    <el-avatar :size="24" :src="approval.user.avatar" />
                    <span class="user-name">{{ approval.user.name }}</span>
                    <span class="approval-time">{{ formatDateTime(approval.createdAt) }}</span>
                  </div>
                  <div class="approval-description" v-if="approval.description">
                    {{ approval.description }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="!approvalHistory.length" class="empty-approval">
              <el-icon><Select /></el-icon>
              <span>暂无审批</span>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>


  </el-dialog>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits, watch, nextTick, onMounted } from 'vue'
import {
  Edit,
  Share,
  MoreFilled,
  Calendar,
  Document,
  Download,
  Plus,
  Right,
  CircleCheck,
  Clock,
  Warning,
  ChatDotRound,
  Operation,
  Select
} from '@element-plus/icons-vue'

// 捕获 ResizeObserver 错误
const originalError = console.error
console.error = (...args) => {
  if (args[0]?.includes?.('ResizeObserver loop completed with undelivered notifications') ||
      args[0]?.includes?.('ResizeObserver loop limit exceeded')) {
    return
  }
  originalError(...args)
}

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  task: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'task-updated'])

// 响应式数据
const visible = ref(false)
const activeTab = ref('details')
const newComment = ref('')
const taskContent = ref('讨论本月工作进展和下月计划')

// 任务清单数据
const checklistItems = ref([
  {
    id: 1,
    content: '介绍上周工作中的重要进展和成果，向参会者汇报',
    duration: '每人2分钟'
  },
  {
    id: 2,
    content: '介绍上周工作中的重要进展和成果，向参会者汇报',
    duration: '每人2分钟'
  },
  {
    id: 3,
    content: '部门负责人总结',
    duration: '10分钟'
  }
])

// 附件数据
const attachments = ref([])

// 参与人数据
const participants = ref([
  {
    id: 1,
    name: '张三',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
  }
])

// 活动数据
const activities = ref([
  {
    id: 1,
    type: 'status_change',
    user: {
      name: '张三',
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
    },
    oldStatus: 'todo',
    newStatus: 'doing',
    createdAt: '2024-06-06 12:18'
  },
  {
    id: 2,
    type: 'status_change',
    user: {
      name: '张三',
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
    },
    oldStatus: 'doing',
    newStatus: 'done',
    createdAt: '2024-06-06 12:18'
  }
])

// 流转历史数据
const workflowHistory = ref([
  {
    id: 1,
    status: 'done',
    user: {
      name: '张三',
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
    },
    createdAt: '2024-06-06 12:18'
  },
  {
    id: 2,
    status: 'doing',
    user: {
      name: '张三',
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
    },
    createdAt: '2024-06-06 12:18'
  }
])

// 审批历史数据
const approvalHistory = ref([])

// 监听props变化
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    // 对话框打开时，延迟处理布局
    nextTick(() => {
      setTimeout(() => {
        window.dispatchEvent(new Event('resize'))
      }, 200)
    })
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 计算属性
// const subtaskProgress = computed(() => {
//   if (!subtasks.value.length) return 0
//   const completed = subtasks.value.filter(task => task.completed).length
//   return Math.round((completed / subtasks.value.length) * 100)
// })

// 方法
const handleClose = () => {
  visible.value = false
}

const handleTabChange = async (tabName) => {
  console.log('切换标签页:', tabName)
  // 使用 nextTick 来避免 ResizeObserver 错误
  await nextTick()
  // 如果切换到详论标签页，需要重新计算表格布局
  if (tabName === 'details') {
    await nextTick()
    // 触发表格重新计算
    setTimeout(() => {
      window.dispatchEvent(new Event('resize'))
    }, 100)
  }
}

const editTask = () => {
  console.log('编辑任务')
}

const getStatusIcon = (status) => {
  const iconMap = {
    'todo': Clock,
    'doing': Warning,
    'done': CircleCheck
  }
  return iconMap[status] || Clock
}

const getStatusClass = (status) => {
  return `status-${status}`
}

const getStatusType = (status) => {
  const typeMap = {
    'todo': 'info',
    'doing': 'warning',
    'done': 'success'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    'todo': '待处理',
    'doing': '进行中',
    'done': '已完成'
  }
  return textMap[status] || status
}

const getApprovalType = (status) => {
  const typeMap = {
    'pending': 'warning',
    'approved': 'success',
    'rejected': 'danger'
  }
  return typeMap[status] || 'info'
}

const getApprovalText = (status) => {
  const textMap = {
    'pending': '待审批',
    'approved': '已通过',
    'rejected': '已拒绝'
  }
  return textMap[status] || status
}

const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('zh-CN', {
    month: 'numeric',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatDateTime = (datetime) => {
  if (!datetime) return ''
  return new Date(datetime).toLocaleString('zh-CN')
}

const formatFileSize = (size) => {
  if (size < 1024) return size + 'B'
  if (size < 1024 * 1024) return Math.round(size / 1024) + 'KB'
  return Math.round(size / (1024 * 1024)) + 'MB'
}

const isOverdue = (date) => {
  if (!date) return false
  return new Date(date) < new Date()
}

const addChecklistItem = () => {
  console.log('添加清单项')
}

const uploadAttachment = () => {
  console.log('上传附件')
}

const downloadFile = (file) => {
  console.log('下载文件:', file.name)
}

const addComment = () => {
  if (!newComment.value.trim()) return

  const comment = {
    id: Date.now(),
    type: 'comment',
    content: newComment.value,
    user: {
      name: '当前用户',
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
    },
    createdAt: new Date().toISOString()
  }

  activities.value.unshift(comment)
  newComment.value = ''
}
</script>

<style scoped>
.task-detail-dialog {
  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;
  }

  .header-left {
    display: flex;
    align-items: center;
  }

  .task-status {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .status-icon {
    font-size: 20px;
  }

  .status-todo {
    color: #909399;
  }

  .status-doing {
    color: #e6a23c;
  }

  .status-done {
    color: #67c23a;
  }

  .task-title {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
  }

  .header-right {
    display: flex;
    gap: 8px;
  }
}

.task-basic-info {
  margin-bottom: 24px;

  .info-row {
    display: flex;
    gap: 32px;
    align-items: center;
  }

  .info-item {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .creator-info {
    display: flex;
    flex-direction: column;

    .creator-name {
      font-size: 14px;
      font-weight: 500;
      color: #303133;
    }

    .create-time {
      font-size: 12px;
      color: #909399;
    }
  }

  .time-info {
    display: flex;
    flex-direction: column;

    .time-label {
      font-size: 14px;
      font-weight: 500;
      color: #303133;

      &.overdue {
        color: #f56c6c;
      }
    }

    .time-desc {
      font-size: 12px;
      color: #909399;
    }
  }
}

.task-tabs {
  .tab-content {
    padding: 16px 0;
    min-height: 400px;
    overflow: hidden;
  }

  /* 防止表格引起的 ResizeObserver 错误 */
  .checklist-table {
    .el-table {
      overflow: hidden;
    }

    .el-table__body-wrapper {
      overflow-x: hidden;
      overflow-y: auto;
    }
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    h4 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  .task-content-section {
    margin-bottom: 24px;

    .task-description {
      .el-textarea {
        :deep(.el-textarea__inner) {
          border: none;
          background: #f5f7fa;
          resize: none;
        }
      }
    }
  }

  .task-checklist-section {
    margin-bottom: 24px;

    .checklist-table {
      .item-number {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        background: #f0f2f5;
        border-radius: 50%;
        font-size: 12px;
        color: #606266;
      }
    }
  }

  .task-attachments-section {
    .attachments-list {
      .attachment-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 0;
        border-bottom: 1px solid #f0f2f5;

        .file-icon {
          color: #409eff;
        }

        .file-name {
          flex: 1;
          font-size: 14px;
          color: #303133;
        }

        .file-size {
          font-size: 12px;
          color: #909399;
        }
      }
    }

    .empty-attachments {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 32px;
      color: #909399;

      .el-icon {
        font-size: 48px;
        margin-bottom: 8px;
      }
    }
  }

  /* 活动标签页样式 */
  .comment-input-section {
    margin-bottom: 24px;

    .comment-input {
      .el-textarea {
        margin-bottom: 12px;
      }

      .comment-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .comment-participants {
          display: flex;
          align-items: center;
          gap: 8px;

          span {
            font-size: 14px;
            color: #606266;
          }

          .participant-avatar {
            margin-left: 4px;
          }
        }
      }
    }
  }

  .activity-list {
    .activity-item {
      display: flex;
      gap: 12px;
      margin-bottom: 16px;

      .activity-content {
        flex: 1;

        .activity-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;

          .user-name {
            font-size: 14px;
            font-weight: 500;
            color: #303133;
          }

          .activity-time {
            font-size: 12px;
            color: #909399;
          }
        }

        .activity-body {
          .comment {
            font-size: 14px;
            color: #606266;
            line-height: 1.5;
          }

          .status-change {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #606266;
          }
        }
      }
    }
  }

  .empty-activities,
  .empty-workflow,
  .empty-approval {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 48px;
    color: #909399;

    .el-icon {
      font-size: 48px;
      margin-bottom: 8px;
    }
  }

  /* 流转标签页样式 */
  .workflow-list {
    .workflow-item {
      display: flex;
      gap: 12px;
      margin-bottom: 16px;

      .workflow-status {
        width: 80px;
        flex-shrink: 0;
      }

      .workflow-content {
        flex: 1;

        .workflow-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;

          .user-name {
            font-size: 14px;
            font-weight: 500;
            color: #303133;
          }

          .workflow-time {
            font-size: 12px;
            color: #909399;
          }
        }

        .workflow-description {
          font-size: 14px;
          color: #606266;
          line-height: 1.5;
        }
      }
    }
  }

  /* 审批标签页样式 */
  .approval-list {
    .approval-item {
      display: flex;
      gap: 12px;
      margin-bottom: 16px;

      .approval-status {
        width: 80px;
        flex-shrink: 0;
      }

      .approval-content {
        flex: 1;

        .approval-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;

          .user-name {
            font-size: 14px;
            font-weight: 500;
            color: #303133;
          }

          .approval-time {
            font-size: 12px;
            color: #909399;
          }
        }

        .approval-description {
          font-size: 14px;
          color: #606266;
          line-height: 1.5;
        }
      }
    }
  }
}
</style>
