{"name": "is-property", "version": "1.0.2", "description": "Tests if a JSON property can be accessed using . syntax", "main": "is-property.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tape": "~1.0.4"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/mi<PERSON><PERSON><PERSON><PERSON>/is-property.git"}, "keywords": ["is", "property", "json", "dot", "bracket", ".", "[]"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "readmeFilename": "README.md", "gitHead": "0a85ea5b6b1264ea1cdecc6e5cf186adbb3ffc50", "bugs": {"url": "https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/is-property/issues"}}