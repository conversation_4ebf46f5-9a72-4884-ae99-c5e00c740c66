const pool = require('../db');

exports.list = async (req, res) => {
  try {
    const [rows] = await pool.query('SELECT * FROM task');
    res.json(rows);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.get = async (req, res) => {
  try {
    const [rows] = await pool.query('SELECT * FROM task WHERE id = ?', [req.params.id]);
    if (rows.length === 0) return res.status(404).json({ error: '未找到任务' });
    res.json(rows[0]);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.create = async (req, res) => {
  try {
    const { project_id, title, description, assignee_id, priority_id, status, start_date, due_date } = req.body;
    const [result] = await pool.query(
      'INSERT INTO task (project_id, title, description, assignee_id, priority_id, status, start_date, due_date) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
      [project_id, title, description, assignee_id, priority_id, status || 'todo', start_date, due_date]
    );
    res.json({ id: result.insertId });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.update = async (req, res) => {
  try {
    const { project_id, title, description, assignee_id, priority_id, status, start_date, due_date } = req.body;
    const [result] = await pool.query(
      'UPDATE task SET project_id=?, title=?, description=?, assignee_id=?, priority_id=?, status=?, start_date=?, due_date=? WHERE id=?',
      [project_id, title, description, assignee_id, priority_id, status, start_date, due_date, req.params.id]
    );
    res.json({ affectedRows: result.affectedRows });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.remove = async (req, res) => {
  try {
    const [result] = await pool.query('DELETE FROM task WHERE id = ?', [req.params.id]);
    res.json({ affectedRows: result.affectedRows });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
}; 