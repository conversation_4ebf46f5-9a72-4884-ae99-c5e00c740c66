const pool = require('../db');

exports.list = async (req, res) => {
  try {
    const [rows] = await pool.query('SELECT * FROM goal');
    res.json(rows);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.get = async (req, res) => {
  try {
    const [rows] = await pool.query('SELECT * FROM goal WHERE id = ?', [req.params.id]);
    if (rows.length === 0) return res.status(404).json({ error: '未找到目标' });
    res.json(rows[0]);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.create = async (req, res) => {
  try {
    const { title, description, type, owner_id, department_id, parent_id, start_date, end_date, progress, status } = req.body;
    const [result] = await pool.query(
      'INSERT INTO goal (title, description, type, owner_id, department_id, parent_id, start_date, end_date, progress, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
      [title, description, type, owner_id, department_id, parent_id, start_date, end_date, progress || 0, status || 1]
    );
    res.json({ id: result.insertId });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.update = async (req, res) => {
  try {
    const { title, description, type, owner_id, department_id, parent_id, start_date, end_date, progress, status } = req.body;
    const [result] = await pool.query(
      'UPDATE goal SET title=?, description=?, type=?, owner_id=?, department_id=?, parent_id=?, start_date=?, end_date=?, progress=?, status=? WHERE id=?',
      [title, description, type, owner_id, department_id, parent_id, start_date, end_date, progress, status, req.params.id]
    );
    res.json({ affectedRows: result.affectedRows });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.remove = async (req, res) => {
  try {
    const [result] = await pool.query('DELETE FROM goal WHERE id = ?', [req.params.id]);
    res.json({ affectedRows: result.affectedRows });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
}; 