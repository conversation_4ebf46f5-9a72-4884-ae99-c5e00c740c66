<template>
  <div class="task-list-container">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" :icon="Plus" @click="showCreateDialog = true">新建任务</el-button>
        <el-button :icon="Filter" @click="showFilterDialog = true">筛选</el-button>
        <el-button :icon="Sort" @click="toggleSort">排序</el-button>
      </div>
      <div class="toolbar-right">
        <el-input
          v-model="searchText"
          placeholder="搜索任务..."
          prefix-icon="Search"
          clearable
          style="width: 200px"
        />
        <el-select v-model="viewMode" style="width: 120px">
          <el-option label="列表视图" value="list" />
          <el-option label="卡片视图" value="card" />
        </el-select>
      </div>
    </div>

    <!-- 任务统计 -->
    <div class="task-stats">
      <div class="stat-item" :class="{ active: statusFilter === '' }" @click="statusFilter = ''">
        <span class="stat-label">全部</span>
        <span class="stat-count">{{ allTasks.length }}</span>
      </div>
      <div class="stat-item" :class="{ active: statusFilter === 'todo' }" @click="statusFilter = 'todo'">
        <span class="stat-label">待处理</span>
        <span class="stat-count">{{ todoTasks.length }}</span>
      </div>
      <div class="stat-item" :class="{ active: statusFilter === 'doing' }" @click="statusFilter = 'doing'">
        <span class="stat-label">进行中</span>
        <span class="stat-count">{{ doingTasks.length }}</span>
      </div>
      <div class="stat-item" :class="{ active: statusFilter === 'done' }" @click="statusFilter = 'done'">
        <span class="stat-label">已完成</span>
        <span class="stat-count">{{ doneTasks.length }}</span>
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="task-content">
      <!-- 列表视图 -->
      <el-table
        v-if="viewMode === 'list'"
        :data="filteredTasks"
        class="task-table"
        @row-click="openTaskDetail"
      >
        <el-table-column width="40">
          <template #default="{ row }">
            <el-checkbox
              v-model="row.completed"
              @change="toggleTaskStatus(row)"
              @click.stop
            />
          </template>
        </el-table-column>

        <el-table-column label="任务" min-width="200">
          <template #default="{ row }">
            <div class="task-info">
              <div class="task-title" :class="{ completed: row.completed }">
                {{ row.title }}
              </div>
              <div class="task-description" v-if="row.description">
                {{ row.description }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="优先级" width="100">
          <template #default="{ row }">
            <el-tag :type="getPriorityType(row.priority)" size="small">
              {{ getPriorityText(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="负责人" width="120">
          <template #default="{ row }">
            <div class="assignee-info" v-if="row.assignee">
              <el-avatar :size="24" :src="row.assignee.avatar" />
              <span class="assignee-name">{{ row.assignee.name }}</span>
            </div>
            <span v-else class="no-assignee">未分配</span>
          </template>
        </el-table-column>

        <el-table-column label="截止时间" width="120">
          <template #default="{ row }">
            <span class="due-date" :class="{ overdue: isOverdue(row.dueDate) }">
              {{ formatDate(row.dueDate) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-dropdown @command="(command) => handleTaskAction(command, row)">
              <el-button text :icon="MoreFilled" />
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit">编辑</el-dropdown-item>
                  <el-dropdown-item command="assign">分配</el-dropdown-item>
                  <el-dropdown-item command="duplicate">复制</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 卡片视图 -->
      <div v-else class="task-cards">
        <div
          v-for="task in filteredTasks"
          :key="task.id"
          class="task-card"
          @click="openTaskDetail(task)"
        >
          <div class="card-header">
            <el-checkbox
              v-model="task.completed"
              @change="toggleTaskStatus(task)"
              @click.stop
            />
            <el-tag :type="getPriorityType(task.priority)" size="small">
              {{ getPriorityText(task.priority) }}
            </el-tag>
          </div>
          <div class="card-content">
            <div class="task-title" :class="{ completed: task.completed }">
              {{ task.title }}
            </div>
            <div class="task-description" v-if="task.description">
              {{ task.description }}
            </div>
          </div>
          <div class="card-footer">
            <div class="assignee-info" v-if="task.assignee">
              <el-avatar :size="20" :src="task.assignee.avatar" />
              <span class="assignee-name">{{ task.assignee.name }}</span>
            </div>
            <span class="due-date" :class="{ overdue: isOverdue(task.dueDate) }">
              {{ formatDate(task.dueDate) }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 新建任务对话框 -->
    <el-dialog v-model="showCreateDialog" title="新建任务" width="600px">
      <el-form :model="newTask" label-width="80px">
        <el-form-item label="任务标题" required>
          <el-input v-model="newTask.title" placeholder="请输入任务标题" />
        </el-form-item>
        <el-form-item label="任务描述">
          <el-input
            v-model="newTask.description"
            type="textarea"
            :rows="3"
            placeholder="请输入任务描述"
          />
        </el-form-item>
        <el-form-item label="优先级">
          <el-select v-model="newTask.priority">
            <el-option label="高" value="high" />
            <el-option label="中" value="medium" />
            <el-option label="低" value="low" />
          </el-select>
        </el-form-item>
        <el-form-item label="负责人">
          <el-select v-model="newTask.assigneeId" placeholder="选择负责人">
            <el-option
              v-for="member in projectMembers"
              :key="member.id"
              :label="member.name"
              :value="member.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="截止日期">
          <el-date-picker
            v-model="newTask.dueDate"
            type="date"
            placeholder="选择截止日期"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="createTask">创建</el-button>
      </template>
    </el-dialog>

    <!-- 任务详情对话框 -->
    <TaskDetailDialog
      v-model="showTaskDetail"
      :task="selectedTask"
      @task-updated="handleTaskUpdated"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Plus, Filter, Sort, Search, MoreFilled } from '@element-plus/icons-vue'
import TaskDetailDialog from '../TaskDetailDialog.vue'

const props = defineProps({
  projectId: {
    type: [String, Number],
    required: true
  }
})

// 响应式数据
const searchText = ref('')
const statusFilter = ref('')
const viewMode = ref('list')
const showCreateDialog = ref(false)
const sortOrder = ref('desc')

// 新建任务表单
const newTask = ref({
  title: '',
  description: '',
  priority: 'medium',
  assigneeId: null,
  dueDate: null
})

// 模拟数据
const tasks = ref([
  {
    id: 1,
    title: '用户登录功能开发',
    description: '实现用户登录、注册、密码重置功能',
    priority: 'high',
    status: 'doing',
    completed: false,
    assignee: {
      id: 1,
      name: '张三',
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
    },
    dueDate: '2024-06-20',
    createdAt: '2024-06-01'
  },
  {
    id: 2,
    title: '数据库设计',
    description: '设计用户表、权限表等核心数据表',
    priority: 'high',
    status: 'done',
    completed: true,
    assignee: {
      id: 2,
      name: '李四',
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
    },
    dueDate: '2024-06-15',
    createdAt: '2024-05-28'
  },
  {
    id: 3,
    title: 'UI界面设计',
    description: '设计系统主要页面的UI界面',
    priority: 'medium',
    status: 'todo',
    completed: false,
    assignee: {
      id: 3,
      name: '王五',
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
    },
    dueDate: '2024-06-25',
    createdAt: '2024-06-02'
  }
])

const projectMembers = ref([
  { id: 1, name: '张三', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
  { id: 2, name: '李四', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
  { id: 3, name: '王五', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' }
])

// 计算属性
const allTasks = computed(() => tasks.value)
const todoTasks = computed(() => tasks.value.filter(task => task.status === 'todo'))
const doingTasks = computed(() => tasks.value.filter(task => task.status === 'doing'))
const doneTasks = computed(() => tasks.value.filter(task => task.status === 'done'))
// 方法
const getPriorityType = (priority) => {
  const typeMap = {
    'high': 'danger',
    'medium': 'warning',
    'low': 'success'
  }
  return typeMap[priority] || ''
}

const getPriorityText = (priority) => {
  const textMap = {
    'high': '高',
    'medium': '中',
    'low': '低'
  }
  return textMap[priority] || priority
}

const getStatusType = (status) => {
  const typeMap = {
    'todo': 'info',
    'doing': 'warning',
    'done': 'success'
  }
  return typeMap[status] || ''
}

const getStatusText = (status) => {
  const textMap = {
    'todo': '待处理',
    'doing': '进行中',
    'done': '已完成'
  }
  return textMap[status] || status
}

const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' })
}

const isOverdue = (dueDate) => {
  if (!dueDate) return false
  return new Date(dueDate) < new Date()
}

const toggleTaskStatus = (task) => {
  task.status = task.completed ? 'done' : 'todo'
}

const toggleSort = () => {
  sortOrder.value = sortOrder.value === 'desc' ? 'asc' : 'desc'
}

const showTaskDetail = ref(false)
const selectedTask = ref({})

const openTaskDetail = (task) => {
  selectedTask.value = task
  showTaskDetail.value = true
}

const handleTaskUpdated = (updatedTask) => {
  const index = tasks.value.findIndex(t => t.id === updatedTask.id)
  if (index !== -1) {
    tasks.value[index] = { ...tasks.value[index], ...updatedTask }
  }
}

const handleTaskAction = (command, task) => {
  switch (command) {
    case 'edit':
      console.log('编辑任务:', task.title)
      break
    case 'assign':
      console.log('分配任务:', task.title)
      break
    case 'duplicate':
      console.log('复制任务:', task.title)
      break
    case 'delete':
      console.log('删除任务:', task.title)
      break
  }
}

const createTask = () => {
  const task = {
    id: Date.now(),
    title: newTask.value.title,
    description: newTask.value.description,
    priority: newTask.value.priority,
    status: 'todo',
    completed: false,
    assignee: projectMembers.value.find(m => m.id === newTask.value.assigneeId),
    dueDate: newTask.value.dueDate,
    createdAt: new Date().toISOString().split('T')[0]
  }

  tasks.value.unshift(task)
  showCreateDialog.value = false

  // 重置表单
  newTask.value = {
    title: '',
    description: '',
    priority: 'medium',
    assigneeId: null,
    dueDate: null
  }
}
</script>

<style scoped>
.task-list-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 工具栏 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 任务统计 */
.task-stats {
  display: flex;
  gap: 24px;
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.stat-item:hover {
  background: #f5f7fa;
}

.stat-item.active {
  background: #e1f3ff;
  color: #409eff;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.stat-item.active .stat-label {
  color: #409eff;
}

.stat-count {
  background: #e4e7ed;
  color: #909399;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  min-width: 20px;
  text-align: center;
}

.stat-item.active .stat-count {
  background: #409eff;
  color: #fff;
}

/* 任务内容 */
.task-content {
  flex: 1;
  overflow: hidden;
}

/* 任务表格 */
.task-table {
  height: 100%;
}

.task-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.task-title {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.task-title.completed {
  text-decoration: line-through;
  color: #909399;
}

.task-description {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.assignee-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.assignee-name {
  font-size: 14px;
  color: #606266;
}

.no-assignee {
  font-size: 14px;
  color: #c0c4cc;
}

.due-date {
  font-size: 14px;
  color: #909399;
}

.due-date.overdue {
  color: #f56c6c;
  font-weight: 500;
}

/* 卡片视图 */
.task-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  height: 100%;
  overflow-y: auto;
}

.task-card {
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  height: fit-content;
}

.task-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.card-content {
  margin-bottom: 12px;
}

.card-content .task-title {
  font-size: 16px;
  margin-bottom: 8px;
}

.card-content .task-description {
  font-size: 14px;
  line-height: 1.5;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-footer .assignee-info {
  gap: 6px;
}

.card-footer .assignee-name {
  font-size: 12px;
}

.card-footer .due-date {
  font-size: 12px;
}

/* 表格行悬停效果 */
:deep(.el-table__row) {
  cursor: pointer;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}
</style>
