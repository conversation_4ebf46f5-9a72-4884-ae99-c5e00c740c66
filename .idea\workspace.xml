<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="25b812a2-3370-4470-b31f-86db8d4f8a65" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2y5HrixJgiXCnsIpjvhPwXSpkLc" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "last_opened_file_path": "D:/ORK",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "com.augment.tokenmanager.plugin.AugmentTokenManagerConfigurable",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-b26f3e71634d-JavaScript-WS-251.26094.131" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="25b812a2-3370-4470-b31f-86db8d4f8a65" name="更改" comment="" />
      <created>1749117715269</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749117715269</updated>
      <workItem from="1749117717282" duration="156000" />
      <workItem from="1749117885732" duration="1184000" />
      <workItem from="1749119992590" duration="38000" />
      <workItem from="1749181876209" duration="2577000" />
      <workItem from="1749184495517" duration="157000" />
      <workItem from="1749184673030" duration="607000" />
      <workItem from="1749185305931" duration="116000" />
      <workItem from="1749185430612" duration="35000" />
      <workItem from="1749185522126" duration="170000" />
      <workItem from="1749185702849" duration="514000" />
      <workItem from="1749186345960" duration="222000" />
      <workItem from="1749191682125" duration="1392000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>